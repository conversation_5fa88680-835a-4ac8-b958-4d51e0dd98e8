<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Velha</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1><PERSON><PERSON> da Velha</h1>
        
        <div class="game-info">
            <div class="current-player">
                <span>Vez do jogador:</span>
                <div class="current-player-avatar" id="current-player-avatar">
                    <img src="./lula.jpg" alt="Lula" id="current-player-img" onerror="this.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='inline';">
                    <span style="font-size: 2rem; display: none;" id="current-player-emoji">🔴</span>
                    <span id="current-player-name">Lula</span>
                </div>
            </div>
            <div class="score">
                <div class="score-item">
                    <div class="player-avatar">
                        <img src="./lula.jpg" alt="Lula" class="score-avatar" onerror="this.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='inline';">
                        <span style="font-size: 1.5rem; display: none;">🔴</span>
                        <span class="player-name">Lula</span>
                    </div>
                    <span id="score-x">0</span>
                </div>
                <div class="score-item">
                    <div class="player-avatar">
                        <img src="./bolsonaro.jpg" alt="Bolsonaro" class="score-avatar" onerror="this.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='inline';">
                        <span style="font-size: 1.5rem; display: none;">🟡</span>
                        <span class="player-name">Bolsonaro</span>
                    </div>
                    <span id="score-o">0</span>
                </div>
                <div class="score-item empate">
                    <span class="empate-icon">🤝</span>
                    <span>Empates</span>
                    <span id="score-draw">0</span>
                </div>
            </div>
        </div>

        <div class="game-board" id="game-board">
            <div class="cell" data-index="0"></div>
            <div class="cell" data-index="1"></div>
            <div class="cell" data-index="2"></div>
            <div class="cell" data-index="3"></div>
            <div class="cell" data-index="4"></div>
            <div class="cell" data-index="5"></div>
            <div class="cell" data-index="6"></div>
            <div class="cell" data-index="7"></div>
            <div class="cell" data-index="8"></div>
        </div>

        <div class="game-message" id="game-message"></div>

        <div class="game-controls">
            <button id="restart-btn" class="btn">Reiniciar Jogo</button>
            <button id="reset-score-btn" class="btn btn-secondary">Zerar Placar</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
